---
cluster_name: {{ cluster_name | default('my-cluster') }}

max_workers: 21
idle_timeout_minutes: 5

provider:
  type: aws
  region: {{ region | default('eu-central-1') }}
  availability_zone: {{ availability_zone | default('eu-central-1a') }}
  cache_stopped_nodes: False
  use_internal_ips: true

docker:
  image: {{ head_node.docker_image }}:{{ head_node.docker_image_tag }}
  container_name: "ray_container"
  pull_before_run: false
  run_options:
    - --ulimit nofile=65536:65536
    - --cap-add=SYS_PTRACE
    - -v /var/log/ray_logs:/tmp/ray/

auth:
  ssh_user: ubuntu
  ssh_private_key: ~/.ssh/{{ ssh_key_name }}.pem
  ssh_proxy_command: ssh -i ~/.ssh/{{ ssh_key_name }}.pem -W %h:%p ec2-user@{{ jumphost_ip }}

available_node_types:
  ray.head.default:
    resources: {"CPU": 0, "GPU": 0}
    node_config:
      InstanceType: t2.xlarge
      ImageId: {{ head_node.ami_id }}
      KeyName: {{ ssh_key_name }}
      Monitoring: {'Enabled': {{ detailed_monitoring }}}
      SubnetIds:
        - {{ subnet_id }}
      SecurityGroupIds:
        - {{ security_group_ids }}
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 140
            VolumeType: gp3
      IamInstanceProfile:
        Arn: {{ iam_instance_profile }}
{% for node in gpu_worker_node_groups %}
  ray.worker.gpu_{{ node.name }}:
    min_workers: {{ node.min_workers }}
    max_workers: {{ node.max_workers }}
    resources: {{ node.resources }}
    docker:
      worker_image: {{ node.docker_image }}:{{ node.docker_image_tag }}
      pull_before_run: false
      worker_run_options:
        - --ulimit nofile=65536:65536
        - --cap-add=SYS_PTRACE
        - -v /var/log/ray_logs:/tmp/ray/
        - --gpus all
        - --runtime=nvidia
        - --device /dev/nvidia0:/dev/nvidia0
        - --device /dev/nvidiactl:/dev/nvidiactl
        - --device /dev/nvidia-uvm:/dev/nvidia-uvm
        - --device /dev/nvidia-uvm-tools:/dev/nvidia-uvm-tools
    node_config:
      InstanceType: {{ node.instance_type }}
      ImageId: {{ node.ami_id }}
      KeyName: {{ ssh_key_name }}
      Monitoring: {'Enabled': {{ detailed_monitoring }}}
      SubnetIds:
        - {{ subnet_id }}
      SecurityGroupIds:
        - {{ security_group_ids }}
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 140
            VolumeType: gp3
      IamInstanceProfile:
        Arn: {{ iam_instance_profile }}
    worker_setup_commands:
      - mkdir -p /home/<USER>/.cuju/document_db && wget -P /home/<USER>/.cuju/document_db https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem
      - pip install -U --extra-index-url https://pypi.org/simple boto3
      - pip install py-spy --index-url "https://pypi.org/simple"
      {% if install_dependencies %}
      - aws sts assume-role --role-arn arn:aws:iam::{{ shared_account_id }}:role/CodeArtifactCrossAccountRole --role-session-name codeartifact-access
      - aws codeartifact login --tool pip --domain cuju-ai --domain-owner {{ shared_account_id }} --repository cuju-ai-workbench --region eu-central-1
      - aws codeartifact get-authorization-token --domain cuju-ai --domain-owner {{ shared_account_id }} --region eu-central-1 --query authorizationToken --output text > .code-artifact-token
      - poetry config repositories.aws https://cuju-ai-{{ shared_account_id }}.d.codeartifact.eu-central-1.amazonaws.com/pypi/cuju-ai-workbench/simple/
      - poetry config http-basic.aws aws $(cat .code-artifact-token)
      - rm .code-artifact-token
      - poetry config virtualenvs.create false
      - poetry lock && poetry install
      - pip uninstall --yes onnxruntime
      - pip install onnxruntime-gpu==1.19.0 --index-url "https://pypi.org/simple" --quiet
      {% endif %}
{% endfor %}

  ray.worker.cpu:
    min_workers: {{ cpu_worker_nodes.min_workers | default(1) }}
    max_workers: {{ cpu_worker_nodes.max_workers | default(3) }}
    resources: {{ cpu_worker_nodes.resources }}
    docker:
      worker_image: {{ cpu_worker_nodes.docker_image }}:{{ cpu_worker_nodes.docker_image_tag }}
      pull_before_run: false
      worker_run_options:
        - --cap-add=SYS_PTRACE
        - --ulimit nofile=65536:65536
        - -v /var/log/ray_logs:/tmp/ray/
    node_config:
      InstanceType: {{ cpu_worker_nodes.instance_type }}
      ImageId: {{ cpu_worker_nodes.ami_id }}
      KeyName: {{ ssh_key_name }}
      Monitoring: {'Enabled': {{ detailed_monitoring }}}
      SubnetIds:
        - {{ subnet_id }}
      SecurityGroupIds:
        - {{ security_group_ids }}
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 140
            VolumeType: gp3
      IamInstanceProfile:
        Arn: {{ iam_instance_profile }}

head_node_type: ray.head.default


file_mounts: {
    "/app": "{{ app_path }}",
    "/serve_configs": "{{ serve_configs_path }}",
    "/home/<USER>/.aws/": "{{ aws_config }}",
    "/home/<USER>/ray_jobs": "./ci/scripts/ray_jobs/",
}

# The nvidia-container-runtime related commands are there to fix inconsistent
# availability of nvidia drivers within the containers, where the error
# "Failed to initialize NVML: Unknown Error" would be returned when nvidia-smi is called
# within the container.
# Reference: https://bobcares.com/blog/docker-failed-to-initialize-nvml-unknown-error/
initialization_commands:
  - sudo usermod -aG docker $USER
  - "[ -f /etc/nvidia-container-runtime/config.toml ] && sudo sed -i 's/^#no-cgroups = false$/no-cgroups = false/' /etc/nvidia-container-runtime/config.toml || true"
  - "[ -f /etc/nvidia-container-runtime/config.toml ] && sudo systemctl restart docker || true"
  - sleep 20
  - cd docker-setup && chown -R ubuntu:ubuntu /home/<USER>/docker-setup && sudo -u ubuntu /usr/local/bin/docker-compose up -d
  - aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin {{ shared_account_id }}.dkr.ecr.eu-central-1.amazonaws.com
  - export CLUSTER_NAME={{ cluster_name }} && export NAME=$HOSTNAME
  - |
    sudo bash -c 'cat <<EOF > /etc/logrotate.d/ray-logs
    /var/log/ray_logs/session_*/logs/*.log /var/log/ray_logs/session_*/logs/*.err /var/log/ray_logs/session_*/logs/*.out /var/log/ray_logs/session_*/logs/old/* {
        daily
        rotate 1
        compress
        missingok
        notifempty
        copytruncate
        su ubuntu users
    }
    EOF'

setup_commands:
  - mkdir -p /home/<USER>/.cuju/document_db && wget -P /home/<USER>/.cuju/document_db https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem
  - pip install -U --extra-index-url https://pypi.org/simple boto3
  - pip install py-spy --index-url "https://pypi.org/simple"
{% if install_dependencies %}
  - aws sts assume-role --role-arn arn:aws:iam::{{ shared_account_id }}:role/CodeArtifactCrossAccountRole --role-session-name codeartifact-access
  - aws codeartifact login --tool pip --domain cuju-ai --domain-owner {{ shared_account_id }} --repository cuju-ai-workbench --region eu-central-1
  - aws codeartifact get-authorization-token --domain cuju-ai --domain-owner {{ shared_account_id }} --region eu-central-1 --query authorizationToken --output text > .code-artifact-token
  - poetry config repositories.aws https://cuju-ai-{{ shared_account_id }}.d.codeartifact.eu-central-1.amazonaws.com/pypi/cuju-ai-workbench/simple/
  - poetry config http-basic.aws aws $(cat .code-artifact-token)
  - rm .code-artifact-token
  - poetry config virtualenvs.create false
  - poetry lock && poetry install
{% endif %}

head_start_ray_commands:
  - sudo mkdir -p /etc/prometheus
  - |
    sudo bash -c 'cat <<EOF > /etc/prometheus/prometheus.yml
    global:
      scrape_interval: 10s

    remote_write:
      - url: "http://{{ monitoring_instance_ip }}:9090/api/v1/write"

    # Scrape from Ray.
    scrape_configs:
    - job_name: "{{ cluster_name }}"
      file_sd_configs:
      - files:
        - "/tmp/ray/prom_metrics_service_discovery.json"
    EOF'
  - ray stop
  - ulimit -n 65536; RAY_ROTATION_MAX_BYTES=********; RAY_ROTATION_BACKUP_COUNT=1; ray start --head  --port=6379 --object-manager-port=8076 --autoscaling-config=~/ray_bootstrap_config.yaml --dashboard-host 0.0.0.0 --dashboard-port 8265 --metrics-export-port=8080
  - nohup prometheus --config.file=/etc/prometheus/prometheus.yml --enable-feature=agent &

worker_start_ray_commands:
  - ray stop
  - ulimit -n 65536; ray start --address=$RAY_HEAD_IP:6379 --object-manager-port=8076 --metrics-export-port=8080
