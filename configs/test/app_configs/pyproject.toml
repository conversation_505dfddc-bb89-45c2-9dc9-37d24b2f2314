[tool.poetry]
name = "ai-service"
version = "0.0.1"
description = "Cuju End-to-End Pipeline Service"
authors = [
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON> <<PERSON>.<PERSON>@mail.schwarz>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON> <<PERSON>.<PERSON>@inovex.de>",
    "<PERSON><PERSON><PERSON> <<PERSON>daresh.<PERSON>@mail.schwarz>",
    "<PERSON> <<PERSON>@inovex.de>",
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
readme = "README.md"
license = "tbd"
package-mode = false


[tool.poetry.dependencies]
python = ">=3.12,<3.13"
cuju-e2e-service = { version = "1.0.3.dev41+5cc7f5cf", source = "cuju-ai-workbench", allow-prereleases = true }

[build-system]
requires = ["poetry-core>=1.2.0"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "cuju-ai-workbench"
url = "https://cuju-ai-651706744076.d.codeartifact.eu-central-1.amazonaws.com/pypi/cuju-ai-workbench/simple"
priority = "supplemental"

